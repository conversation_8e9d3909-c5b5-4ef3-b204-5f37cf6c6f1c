// DESCENT - Game Controls System
class GameControls {
    constructor(camera, domElement) {
        this.camera = camera;
        this.domElement = domElement;
        
        // Movement state
        this.moveForward = false;
        this.moveBackward = false;
        this.moveLeft = false;
        this.moveRight = false;
        
        // Movement settings
        this.movementSpeed = 5.0;
        this.mouseSensitivity = 0.002;
        
        // Pointer lock
        this.isLocked = false;
        this.controls = null;
        
        // Collision detection
        this.raycaster = new THREE.Raycaster();
        this.collisionDistance = 0.5;
        
        // Movement vectors
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();
        
        // Callback for position updates
        this.onMove = null;
        
        this.enabled = false;
    }
    
    init() {
        this.setupPointerLock();
        this.setupKeyboardControls();
        this.setupMouseControls();
        
        console.log('Game controls initialized');
    }
    
    setupPointerLock() {
        // Create pointer lock controls
        this.controls = new THREE.PointerLockControls(this.camera, this.domElement);
        
        // Pointer lock event listeners
        this.controls.addEventListener('lock', () => {
            this.isLocked = true;
            console.log('Pointer locked');
        });
        
        this.controls.addEventListener('unlock', () => {
            this.isLocked = false;
            console.log('Pointer unlocked');
        });
        
        // Click to lock pointer (when game starts)
        this.domElement.addEventListener('click', () => {
            if (this.enabled && !this.isLocked) {
                this.controls.lock();
            }
        });
        
        // ESC to unlock
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Escape' && this.isLocked) {
                this.controls.unlock();
            }
        });
    }
    
    setupKeyboardControls() {
        // Key down events
        document.addEventListener('keydown', (event) => {
            if (!this.enabled) return;
            
            switch (event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    this.moveForward = true;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    this.moveBackward = true;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    this.moveLeft = true;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    this.moveRight = true;
                    break;
            }
        });
        
        // Key up events
        document.addEventListener('keyup', (event) => {
            if (!this.enabled) return;
            
            switch (event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    this.moveForward = false;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    this.moveBackward = false;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    this.moveLeft = false;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    this.moveRight = false;
                    break;
            }
        });
    }
    
    setupMouseControls() {
        // Mouse movement is handled by PointerLockControls
        // Additional mouse events can be added here if needed
    }
    
    update(deltaTime) {
        if (!this.enabled || !this.isLocked) return;
        
        // Reset velocity
        this.velocity.x -= this.velocity.x * 10.0 * deltaTime;
        this.velocity.z -= this.velocity.z * 10.0 * deltaTime;
        
        // Calculate movement direction
        this.direction.z = Number(this.moveForward) - Number(this.moveBackward);
        this.direction.x = Number(this.moveRight) - Number(this.moveLeft);
        this.direction.normalize();
        
        // Apply movement
        if (this.moveForward || this.moveBackward) {
            this.velocity.z -= this.direction.z * this.movementSpeed * deltaTime;
        }
        if (this.moveLeft || this.moveRight) {
            this.velocity.x -= this.direction.x * this.movementSpeed * deltaTime;
        }
        
        // Store old position for collision detection
        const oldPosition = this.camera.position.clone();
        
        // Apply velocity to camera
        this.controls.moveRight(-this.velocity.x * deltaTime);
        this.controls.moveForward(-this.velocity.z * deltaTime);
        
        // Check for collisions
        if (this.checkCollisions()) {
            // Revert to old position if collision detected
            this.camera.position.copy(oldPosition);
        }
        
        // Keep camera at eye level
        this.camera.position.y = 1.7;
        
        // Callback for position updates
        if (this.onMove) {
            this.onMove(this.camera.position);
        }
    }
    
    checkCollisions() {
        // Cast rays in movement directions to detect collisions
        const directions = [
            new THREE.Vector3(0, 0, 1),   // forward
            new THREE.Vector3(0, 0, -1),  // backward
            new THREE.Vector3(-1, 0, 0),  // left
            new THREE.Vector3(1, 0, 0),   // right
        ];
        
        for (let direction of directions) {
            this.raycaster.set(this.camera.position, direction);
            
            // Get intersections with scene objects
            const intersections = this.raycaster.intersectObjects(
                this.getCollidableObjects(), 
                true
            );
            
            if (intersections.length > 0 && intersections[0].distance < this.collisionDistance) {
                return true; // Collision detected
            }
        }
        
        return false; // No collision
    }
    
    getCollidableObjects() {
        // Return objects that should block player movement
        // This will be populated by scene objects
        if (window.game && window.game.scene) {
            return window.game.scene.children.filter(child => {
                return child.userData.collidable === true;
            });
        }
        return [];
    }
    
    enable() {
        this.enabled = true;
        console.log('Controls enabled');
    }
    
    disable() {
        this.enabled = false;
        this.isLocked = false;
        
        // Stop all movement
        this.moveForward = false;
        this.moveBackward = false;
        this.moveLeft = false;
        this.moveRight = false;
        
        // Unlock pointer
        if (this.controls && this.controls.isLocked) {
            this.controls.unlock();
        }
        
        console.log('Controls disabled');
    }
    
    setPosition(x, y, z) {
        this.camera.position.set(x, y, z);
    }
    
    lookAt(x, y, z) {
        this.camera.lookAt(x, y, z);
    }
    
    // Get current look direction
    getDirection() {
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        return direction;
    }
    
    // Get current position
    getPosition() {
        return this.camera.position.clone();
    }
    
    // Set movement speed
    setMovementSpeed(speed) {
        this.movementSpeed = speed;
    }
    
    // Set mouse sensitivity
    setMouseSensitivity(sensitivity) {
        this.mouseSensitivity = sensitivity;
    }
}
