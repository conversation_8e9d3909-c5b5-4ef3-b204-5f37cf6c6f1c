// DESCENT - Menu System
class MenuSystem {
    constructor() {
        this.glitchInterval = null;
        this.titleElement = null;
        this.menuAudio = null;
        this.init();
    }
    
    init() {
        this.titleElement = document.getElementById('gameTitle');
        this.menuAudio = document.getElementById('menuAudio');
        
        this.setupEventListeners();
        this.startTitleGlitch();
        this.playMenuAudio();
        
        console.log('Menu system initialized');
    }
    
    setupEventListeners() {
        // Start button
        document.getElementById('startButton').addEventListener('click', () => {
            this.playButtonClick();
            this.startGame();
        });
        
        // Instructions button
        document.getElementById('instructionsButton').addEventListener('click', () => {
            this.playButtonClick();
            this.showInstructions();
        });
        
        // Quit button
        document.getElementById('quitButton').addEventListener('click', () => {
            this.playButtonClick();
            this.quitGame();
        });
        
        // Close instructions
        document.getElementById('closeInstructions').addEventListener('click', () => {
            this.hideInstructions();
        });
        
        // Button hover effects
        const buttons = document.querySelectorAll('.menu-button');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.playButtonHover();
                this.triggerButtonGlitch(button);
            });
        });
        
        // Escape key to close instructions
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Escape') {
                this.hideInstructions();
            }
        });
    }
    
    playMenuAudio() {
        if (this.menuAudio) {
            this.menuAudio.volume = 0.3;
            this.menuAudio.play().catch(e => {
                console.log('Menu audio autoplay blocked, will play on first interaction');
            });
        }
    }
    
    playButtonHover() {
        // Create and play hover sound
        const audio = new Audio('assets/audio/static_hover.wav');
        audio.volume = 0.2;
        audio.play().catch(e => console.log('Hover sound failed to play'));
    }
    
    playButtonClick() {
        // Create and play click sound
        const audio = new Audio('assets/audio/elevator_ding.wav');
        audio.volume = 0.4;
        audio.play().catch(e => console.log('Click sound failed to play'));
    }
    
    startTitleGlitch() {
        // Random glitch effect every 4-6 seconds
        const scheduleNextGlitch = () => {
            const delay = 4000 + Math.random() * 2000; // 4-6 seconds
            setTimeout(() => {
                this.triggerTitleGlitch();
                scheduleNextGlitch();
            }, delay);
        };
        
        scheduleNextGlitch();
    }
    
    triggerTitleGlitch() {
        if (!this.titleElement) return;
        
        // Horizontal displacement glitch
        this.titleElement.classList.add('glitch');
        
        // Random chance for RGB split effect
        if (Math.random() < 0.3) {
            this.createRGBSplit();
        }
        
        // Flicker effect
        this.titleElement.style.opacity = '0.7';
        
        // Play glitch sound
        const glitchAudio = new Audio('assets/audio/digital_distortion.wav');
        glitchAudio.volume = 0.1;
        glitchAudio.play().catch(e => console.log('Glitch sound failed to play'));
        
        // Reset after glitch duration
        setTimeout(() => {
            this.titleElement.classList.remove('glitch');
            this.titleElement.style.opacity = '1';
        }, 200);
    }
    
    createRGBSplit() {
        // Create a duplicate red layer for RGB split effect
        const duplicate = this.titleElement.cloneNode(true);
        duplicate.style.position = 'absolute';
        duplicate.style.color = '#FF0000';
        duplicate.style.opacity = '0.5';
        duplicate.style.transform = 'translateX(3px)';
        duplicate.style.zIndex = '-1';
        
        this.titleElement.parentNode.appendChild(duplicate);
        
        // Remove duplicate after effect
        setTimeout(() => {
            if (duplicate.parentNode) {
                duplicate.parentNode.removeChild(duplicate);
            }
        }, 100);
    }
    
    triggerButtonGlitch(button) {
        // Micro-glitch effect on hover
        const originalTransform = button.style.transform;
        button.style.transform = 'translateX(2px)';
        
        setTimeout(() => {
            button.style.transform = originalTransform;
        }, 50);
    }
    
    showInstructions() {
        const overlay = document.getElementById('instructionsOverlay');
        overlay.style.display = 'flex';
        
        // Fade in animation
        overlay.style.opacity = '0';
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);
    }
    
    hideInstructions() {
        const overlay = document.getElementById('instructionsOverlay');
        overlay.style.opacity = '0';
        
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
    }
    
    startGame() {
        // Pause background video briefly
        const video = document.getElementById('backgroundVideo');
        if (video) {
            video.pause();
            setTimeout(() => {
                video.play();
            }, 200);
        }
        
        // White flash transition
        this.createWhiteFlash();
        
        // Stop menu audio
        if (this.menuAudio) {
            this.menuAudio.pause();
        }
        
        // Start game after flash
        setTimeout(() => {
            if (window.game) {
                window.game.startGame();
            }
        }, 500);
    }
    
    createWhiteFlash() {
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.backgroundColor = '#FFFFFF';
        flash.style.zIndex = '1000';
        flash.style.opacity = '0';
        flash.style.transition = 'opacity 0.2s ease';
        
        document.body.appendChild(flash);
        
        // Trigger flash
        setTimeout(() => {
            flash.style.opacity = '1';
        }, 10);
        
        // Remove flash
        setTimeout(() => {
            flash.style.opacity = '0';
            setTimeout(() => {
                if (flash.parentNode) {
                    document.body.removeChild(flash);
                }
            }, 200);
        }, 200);
    }
    
    quitGame() {
        // Fade to black
        const fadeOut = document.createElement('div');
        fadeOut.style.position = 'fixed';
        fadeOut.style.top = '0';
        fadeOut.style.left = '0';
        fadeOut.style.width = '100%';
        fadeOut.style.height = '100%';
        fadeOut.style.backgroundColor = '#000000';
        fadeOut.style.zIndex = '1000';
        fadeOut.style.opacity = '0';
        fadeOut.style.transition = 'opacity 1s ease';
        
        document.body.appendChild(fadeOut);
        
        // Start fade
        setTimeout(() => {
            fadeOut.style.opacity = '1';
        }, 10);
        
        // Close after fade
        setTimeout(() => {
            // Try to close browser tab/window
            if (window.close) {
                window.close();
            } else {
                // Fallback: redirect to blank page
                window.location.href = 'about:blank';
            }
        }, 1000);
    }
}

// Initialize menu system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MenuSystem();
});
