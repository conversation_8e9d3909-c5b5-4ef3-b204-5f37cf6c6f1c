# DESCENT - Browser-Based Horror Game

A photorealistic 3D horror game based on the Elevator Game urban legend, built entirely for the browser using Three.js.

## 🎮 Game Concept

**DESCENT** is a first-person horror experience where players:
- Explore an abandoned hotel lobby
- Enter a realistic elevator
- Perform the ritual button sequence: **4 → 2 → 6 → 2 → 10 → 5**
- Get transported to an eerie Other World
- Must return safely or face terrifying consequences

## 🚀 Features

### Visual
- **Photorealistic 3D environments** using Three.js
- **Dynamic lighting system** with realistic flickering effects
- **PBR materials** for authentic surface rendering
- **Real-time shadows** and atmospheric fog
- **Cinematic main menu** with video background

### Audio
- **Layered ambient soundscapes** using Howler.js
- **3D positional audio** for immersive experience
- **Dynamic sound effects** synchronized with visual events
- **Atmospheric music** that adapts to game state

### Gameplay
- **First-person controls** with WASD movement and mouse look
- **Interactive elevator buttons** with visual feedback
- **Ritual sequence tracking** with consequences for mistakes
- **Multiple scenes** with seamless transitions
- **Jump scares** and tension-building mechanics

## 🛠️ Technical Stack

- **Three.js** - 3D rendering engine
- **Howler.js** - Audio management
- **GLTFLoader** - 3D model loading
- **PointerLockControls** - First-person camera controls
- **Vanilla JavaScript** - No frameworks, pure web technologies
- **HTML5 Canvas** - Rendering target
- **CSS3** - UI styling and animations

## 📁 Project Structure

```
DESCENT/
├── index.html              # Main entry point
├── js/
│   ├── main.js            # Core game engine
│   ├── menu.js            # Menu system with glitch effects
│   ├── controls.js        # WASD + mouse look controls
│   ├── audio.js           # Audio management system
│   ├── lighting.js        # Dynamic lighting with flicker effects
│   └── scenes/
│       ├── lobby.js       # Abandoned hotel lobby scene
│       ├── elevator.js    # Interactive elevator scene
│       └── otherworld.js  # Twisted Other World scene
├── assets/
│   ├── models/           # 3D models (.glb files)
│   ├── audio/            # Sound effects and ambient audio
│   ├── textures/         # PBR texture maps
│   └── video/            # Background video for menu
└── README.md
```

## 🎯 Getting Started

### Prerequisites
- Modern web browser with WebGL support
- Local web server (for loading assets)

### Quick Start
1. **Clone or download** this repository
2. **Add your assets** to the appropriate folders:
   - Place 3D models in `assets/models/`
   - Add audio files to `assets/audio/`
   - Put textures in `assets/textures/`
   - Add menu video to `assets/video/`
3. **Start a local web server**:
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (if you have http-server installed)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```
4. **Open your browser** and navigate to `http://localhost:8000`

### Asset Requirements

#### 3D Models (GLB format)
- `lobby.glb` - Hotel lobby environment
- `elevator.glb` - Elevator interior
- `otherworld.glb` - Other World hallway

#### Audio Files
- `menu_idle.ogg` - Menu ambient sound
- `lobby_ambient.ogg` - Lobby background audio
- `elevator_hum.ogg` - Elevator ambient sound
- `otherworld_ambient.ogg` - Other World atmosphere
- Various sound effects (see `assets/audio/README.md`)

#### Video
- `menu_bg_loop.mp4` - Menu background video (1920x1080, seamless loop)

## 🎮 Controls

- **WASD** - Movement
- **Mouse** - Look around
- **E** - Interact (enter elevator, press buttons)
- **Click** - Press elevator buttons
- **ESC** - Release mouse lock / Close menus

## 🎨 Customization

### Modifying Scenes
Each scene is modular and can be easily customized:
- Edit `js/scenes/lobby.js` for lobby modifications
- Modify `js/scenes/elevator.js` for elevator changes
- Adjust `js/scenes/otherworld.js` for Other World atmosphere

### Audio Settings
Adjust audio levels in `js/audio.js`:
```javascript
this.musicVolume = 0.7;
this.sfxVolume = 0.8;
this.ambientVolume = 0.5;
```

### Lighting Effects
Customize lighting in `js/lighting.js`:
```javascript
this.flickerIntensity = 0.3;
this.flickerSpeed = 2.0;
this.randomFlickerChance = 0.02;
```

## 🔧 Development

### Adding New Scenes
1. Create a new scene class in `js/scenes/`
2. Implement required methods: `load()`, `update()`, `dispose()`
3. Add scene switching logic in `main.js`

### Performance Optimization
- Use Draco compression for 3D models
- Optimize textures (power-of-2 dimensions)
- Implement LOD (Level of Detail) for distant objects
- Use object pooling for frequently created/destroyed objects

## 🌐 Browser Compatibility

- **Chrome** 80+ (recommended)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

Requires WebGL 2.0 support for optimal performance.

## 📱 Mobile Support

While designed for desktop, the game includes responsive design elements. For mobile deployment:
- Implement touch controls
- Optimize performance for mobile GPUs
- Adjust UI scaling for smaller screens

## 🚨 Known Issues

- Audio autoplay may be blocked by browser policies (user interaction required)
- Large asset files may cause loading delays on slow connections
- Some older graphics cards may experience performance issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source. Please respect any third-party asset licenses.

## 🎬 Credits

- **Three.js** - 3D graphics library
- **Howler.js** - Audio library
- **Elevator Game** - Urban legend inspiration

---

**Warning**: This game contains horror elements including jump scares, disturbing imagery, and intense audio. Player discretion advised.
