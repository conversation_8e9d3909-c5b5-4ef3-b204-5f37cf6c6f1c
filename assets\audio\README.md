# Audio Assets Directory

Place your audio files here:

## Required Audio Files:

### Menu Audio:
- `menu_idle.ogg` - Menu background ambient sound
- `static_hover.wav` - Button hover sound effect
- `elevator_ding.wav` - Button click/elevator ding sound
- `digital_distortion.wav` - Title glitch sound effect

### Game Audio:
- `lobby_ambient.ogg` - Lobby background ambient loop
- `elevator_hum.ogg` - Elevator ambient hum loop
- `otherworld_ambient.ogg` - Other World ambient loop
- `light_flicker.ogg` - Light flickering sound loop
- `jumpscare.wav` - Jump scare sound effect
- `door_open.wav` - Door opening sound
- `door_close.wav` - Door closing sound
- `elevator_move.wav` - Elevator movement sound
- `footstep.wav` - Footstep sound effect
- `heartbeat.wav` - Heartbeat loop for tension

### Video:
- `menu_bg_loop.mp4` - Menu background video loop

## Audio Requirements:
- Format: OGG for loops, WAV for short effects
- Quality: 44.1kHz, 16-bit minimum
- Compression: Use OGG Vorbis for size optimization
- Volume: Normalize to prevent clipping
- Loops: Ensure seamless looping for ambient sounds

## Recommended Tools:
- Audacity (free) - for editing and converting audio
- Freesound.org - for royalty-free sound effects
- Zapsplat - for professional sound effects (requires account)

## Audio Guidelines:
- Keep file sizes reasonable for web loading
- Use stereo for ambient sounds, mono for effects
- Test loop points for seamless playback
- Consider browser audio format support (OGG, MP3, WAV)

## Placeholder Audio:
If audio files are missing, the game will continue without sound or use browser-generated tones where possible.
