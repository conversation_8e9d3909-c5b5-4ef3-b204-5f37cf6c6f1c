<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DESCENT - Elevator Game</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Montserrat:wght@400;600;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Montserrat', sans-serif;
            background: #000;
            overflow: hidden;
            cursor: none;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #backgroundVideo {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
        }
        
        #mainMenu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding-top: 20vh;
        }
        
        #gameTitle {
            font-family: '<PERSON><PERSON> Neue', cursive;
            font-size: clamp(4rem, 8vw, 8rem);
            color: #FFFFFF;
            text-align: center;
            margin-bottom: 10vh;
            filter: brightness(0.9) drop-shadow(0 0 20px #8B0000);
            animation: titlePulse 3s ease-in-out infinite;
        }
        
        @keyframes titlePulse {
            0%, 100% { filter: brightness(0.9) drop-shadow(0 0 20px #8B0000); }
            50% { filter: brightness(1) drop-shadow(0 0 30px #8B0000); }
        }
        
        .glitch {
            animation: glitch 0.2s ease-in-out;
        }
        
        @keyframes glitch {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-3px); }
            100% { transform: translateX(0); }
        }
        
        #menuButtons {
            display: flex;
            flex-direction: column;
            gap: 50px;
            align-items: center;
        }
        
        .menu-button {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: clamp(1.2rem, 2.5vw, 2rem);
            color: #CCCCCC;
            background: rgba(0, 0, 0, 0.2);
            border: none;
            padding: 15px 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .menu-button:hover {
            color: #FFFFFF;
            background: rgba(0, 0, 0, 0.4);
        }
        
        .menu-button:active {
            color: #FF0000;
        }
        
        #instructionsOverlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 20;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        #instructionsContent {
            background: rgba(0, 0, 0, 0.8);
            padding: 40px;
            border-radius: 10px;
            color: #FFFFFF;
            font-family: 'Montserrat', sans-serif;
            font-size: clamp(1rem, 2vw, 1.5rem);
            line-height: 1.8;
            text-align: left;
            position: relative;
            max-width: 600px;
        }
        
        #closeInstructions {
            position: absolute;
            top: 10px;
            right: 20px;
            background: none;
            border: none;
            color: #FFFFFF;
            font-size: 2rem;
            cursor: pointer;
            animation: flicker 2s ease-in-out infinite;
        }
        
        @keyframes flicker {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        #gameCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            display: none;
        }
        
        #gameUI {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 15;
            pointer-events: none;
            display: none;
        }
        
        #interactionPrompt {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            color: #FFFFFF;
            font-family: 'Montserrat', sans-serif;
            font-size: 1.2rem;
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 20px;
            border-radius: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #FFFFFF;
            border-radius: 50%;
            opacity: 0.7;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #FFFFFF;
            font-family: 'Montserrat', sans-serif;
            font-size: 1.5rem;
            z-index: 100;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- Background Video for Menu -->
        <video id="backgroundVideo" autoplay muted loop>
            <source src="assets/video/menu_bg_loop.mp4" type="video/mp4">
        </video>
        
        <!-- Main Menu -->
        <div id="mainMenu">
            <h1 id="gameTitle">DESCENT</h1>
            <div id="menuButtons">
                <button class="menu-button" id="startButton">START RITUAL</button>
                <button class="menu-button" id="instructionsButton">INSTRUCTIONS</button>
                <button class="menu-button" id="quitButton">QUIT</button>
            </div>
        </div>
        
        <!-- Instructions Overlay -->
        <div id="instructionsOverlay">
            <div id="instructionsContent">
                <button id="closeInstructions">&times;</button>
                <h2>FOLLOW THE RITUAL:</h2>
                <p>4 → 2 → 6 → 2 → 10 → 5</p>
                <br>
                <p>DO NOT LEAVE THE ELEVATOR</p>
                <p>DO NOT LOOK AT HER</p>
                <p>DO NOT SPEAK</p>
            </div>
        </div>
        
        <!-- Game Canvas -->
        <canvas id="gameCanvas"></canvas>
        
        <!-- Game UI -->
        <div id="gameUI">
            <div id="crosshair"></div>
            <div id="interactionPrompt">Press E to interact</div>
        </div>
        
        <!-- Loading Screen -->
        <div id="loadingScreen" class="loading">Loading...</div>
    </div>
    
    <!-- Audio Elements -->
    <audio id="menuAudio" loop>
        <source src="assets/audio/menu_idle.ogg" type="audio/ogg">
    </audio>
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/PointerLockControls.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.2.3/howler.min.js"></script>
    
    <script src="js/audio.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/lighting.js"></script>
    <script src="js/scenes/lobby.js"></script>
    <script src="js/scenes/elevator.js"></script>
    <script src="js/scenes/otherworld.js"></script>
    <script src="js/main.js"></script>
    <script src="js/menu.js"></script>
</body>
</html>
