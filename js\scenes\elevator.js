// DESCENT - Elevator Scene
class ElevatorScene {
    constructor(scene) {
        this.scene = scene;
        this.loader = new THREE.GLTFLoader();
        
        this.objects = [];
        this.buttons = [];
        this.buttonMeshes = [];
        this.loaded = false;
        
        // Elevator state
        this.doorsOpen = false;
        this.moving = false;
        
        // Button layout (3x4 grid)
        this.buttonLayout = [
            [null, 'UP', null],
            ['10', '11', '12'],
            ['7', '8', '9'],
            ['4', '5', '6'],
            ['1', '2', '3'],
            [null, 'DOWN', null]
        ];
    }
    
    async load() {
        console.log('Loading elevator scene...');
        
        try {
            // Try to load elevator model
            await this.loadElevatorModel();
            
            // Create placeholder if model fails
            this.createPlaceholderElevator();
            
            // Create interactive button panel
            this.createButtonPanel();
            
            // Set up elevator lighting
            if (window.game && window.game.lights) {
                window.game.lights.setElevatorLighting();
            }
            
            // Start elevator audio
            if (window.game && window.game.audioManager) {
                window.game.audioManager.startElevatorAudio();
            }
            
            this.loaded = true;
            console.log('Elevator scene loaded successfully');
            
        } catch (error) {
            console.error('Error loading elevator scene:', error);
            this.createFallbackScene();
        }
    }
    
    async loadElevatorModel() {
        return new Promise((resolve, reject) => {
            this.loader.load(
                'assets/models/elevator.glb',
                (gltf) => {
                    console.log('Elevator model loaded');
                    
                    const model = gltf.scene;
                    model.position.set(0, 0, 0);
                    model.scale.set(1, 1, 1);
                    
                    // Configure model materials and shadows
                    model.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                            child.userData.collidable = true;
                            
                            // Enhance materials for elevator atmosphere
                            if (child.material) {
                                child.material.roughness = 0.3;
                                child.material.metalness = 0.7;
                            }
                        }
                    });
                    
                    this.scene.add(model);
                    this.objects.push(model);
                    resolve();
                },
                (progress) => {
                    console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
                },
                (error) => {
                    console.warn('Failed to load elevator model, using placeholder');
                    resolve(); // Don't reject, use placeholder instead
                }
            );
        });
    }
    
    createPlaceholderElevator() {
        // Elevator interior dimensions
        const width = 2;
        const height = 3;
        const depth = 2;
        
        // Floor
        const floorGeometry = new THREE.PlaneGeometry(width, depth);
        const floorMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x666666,
            roughness: 0.8
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = 0;
        floor.receiveShadow = true;
        this.scene.add(floor);
        this.objects.push(floor);
        
        // Walls
        this.createElevatorWalls(width, height, depth);
        
        // Ceiling
        const ceilingGeometry = new THREE.PlaneGeometry(width, depth);
        const ceilingMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
        const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
        ceiling.rotation.x = Math.PI / 2;
        ceiling.position.y = height;
        ceiling.receiveShadow = true;
        this.scene.add(ceiling);
        this.objects.push(ceiling);
        
        // Elevator light
        this.createElevatorLight();
    }
    
    createElevatorWalls(width, height, depth) {
        const wallMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x777777,
            roughness: 0.3,
            metalness: 0.5
        });
        
        // Back wall
        const backWallGeometry = new THREE.PlaneGeometry(width, height);
        const backWall = new THREE.Mesh(backWallGeometry, wallMaterial);
        backWall.position.set(0, height / 2, -depth / 2);
        backWall.castShadow = true;
        backWall.receiveShadow = true;
        backWall.userData.collidable = true;
        this.scene.add(backWall);
        this.objects.push(backWall);
        
        // Left wall
        const leftWallGeometry = new THREE.PlaneGeometry(depth, height);
        const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial);
        leftWall.rotation.y = Math.PI / 2;
        leftWall.position.set(-width / 2, height / 2, 0);
        leftWall.castShadow = true;
        leftWall.receiveShadow = true;
        leftWall.userData.collidable = true;
        this.scene.add(leftWall);
        this.objects.push(leftWall);
        
        // Right wall (where buttons will be)
        const rightWallGeometry = new THREE.PlaneGeometry(depth, height);
        const rightWall = new THREE.Mesh(rightWallGeometry, wallMaterial);
        rightWall.rotation.y = -Math.PI / 2;
        rightWall.position.set(width / 2, height / 2, 0);
        rightWall.castShadow = true;
        rightWall.receiveShadow = true;
        rightWall.userData.collidable = true;
        this.scene.add(rightWall);
        this.objects.push(rightWall);
    }
    
    createElevatorLight() {
        // Fluorescent light fixture
        const lightGeometry = new THREE.BoxGeometry(1.5, 0.1, 1.5);
        const lightMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xffffff,
            emissive: 0x444444
        });
        const lightFixture = new THREE.Mesh(lightGeometry, lightMaterial);
        lightFixture.position.set(0, 2.9, 0);
        this.scene.add(lightFixture);
        this.objects.push(lightFixture);
    }
    
    createButtonPanel() {
        // Button panel background
        const panelGeometry = new THREE.BoxGeometry(0.6, 1.2, 0.05);
        const panelMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            roughness: 0.2,
            metalness: 0.8
        });
        const panel = new THREE.Mesh(panelGeometry, panelMaterial);
        panel.position.set(0.9, 1.5, 0);
        panel.castShadow = true;
        panel.receiveShadow = true;
        this.scene.add(panel);
        this.objects.push(panel);
        
        // Create individual buttons
        this.createButtons();
    }
    
    createButtons() {
        const buttonSize = 0.08;
        const buttonSpacing = 0.12;
        const startX = 0.92;
        const startY = 2.0;
        
        this.buttonLayout.forEach((row, rowIndex) => {
            row.forEach((buttonLabel, colIndex) => {
                if (buttonLabel) {
                    const button = this.createButton(
                        buttonLabel,
                        startX,
                        startY - (rowIndex * buttonSpacing),
                        (colIndex - 1) * buttonSpacing,
                        buttonSize
                    );
                    this.buttons.push(button);
                    this.buttonMeshes.push(button.mesh);
                }
            });
        });
        
        console.log(`Created ${this.buttons.length} elevator buttons`);
    }
    
    createButton(label, baseX, y, offsetX, size) {
        // Button geometry
        const buttonGeometry = new THREE.CylinderGeometry(size / 2, size / 2, 0.02, 16);
        const buttonMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x666666,
            roughness: 0.3,
            metalness: 0.7
        });
        const buttonMesh = new THREE.Mesh(buttonGeometry, buttonMaterial);
        buttonMesh.position.set(baseX, y, offsetX);
        buttonMesh.rotation.z = Math.PI / 2;
        buttonMesh.castShadow = true;
        buttonMesh.receiveShadow = true;
        
        // Store button data
        buttonMesh.userData.buttonNumber = parseInt(label) || label;
        buttonMesh.userData.isButton = true;
        buttonMesh.userData.originalColor = 0x666666;
        buttonMesh.userData.pressed = false;
        
        this.scene.add(buttonMesh);
        this.objects.push(buttonMesh);
        
        // Button label (text)
        this.createButtonLabel(label, baseX + 0.01, y, offsetX);
        
        return {
            mesh: buttonMesh,
            label: label,
            number: parseInt(label) || label
        };
    }
    
    createButtonLabel(text, x, y, z) {
        // Create a simple text texture
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const context = canvas.getContext('2d');
        
        context.fillStyle = '#000000';
        context.fillRect(0, 0, 64, 64);
        
        context.fillStyle = '#FFFFFF';
        context.font = 'bold 24px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(text, 32, 32);
        
        const texture = new THREE.CanvasTexture(canvas);
        const labelMaterial = new THREE.MeshBasicMaterial({ 
            map: texture,
            transparent: true
        });
        
        const labelGeometry = new THREE.PlaneGeometry(0.06, 0.06);
        const labelMesh = new THREE.Mesh(labelGeometry, labelMaterial);
        labelMesh.position.set(x, y, z);
        labelMesh.rotation.y = -Math.PI / 2;
        
        this.scene.add(labelMesh);
        this.objects.push(labelMesh);
    }
    
    pressButton(buttonNumber) {
        console.log(`Button ${buttonNumber} pressed`);
        
        // Find the button
        const button = this.buttons.find(b => b.number === buttonNumber);
        if (!button) {
            console.warn(`Button ${buttonNumber} not found`);
            return;
        }
        
        // Visual feedback
        this.animateButtonPress(button.mesh);
        
        // Audio feedback
        if (window.game && window.game.audioManager) {
            window.game.audioManager.playSound('elevator_ding');
        }
        
        // Light up button
        button.mesh.material.color.setHex(0xff6666);
        button.mesh.userData.pressed = true;
        
        // Add to sequence in main game
        if (window.game) {
            window.game.pressElevatorButton(buttonNumber);
        }
    }
    
    animateButtonPress(buttonMesh) {
        const originalPosition = buttonMesh.position.x;
        
        // Press in
        buttonMesh.position.x = originalPosition - 0.01;
        
        // Return to original position
        setTimeout(() => {
            buttonMesh.position.x = originalPosition;
        }, 100);
    }
    
    resetButtons() {
        this.buttons.forEach(button => {
            button.mesh.material.color.setHex(button.mesh.userData.originalColor);
            button.mesh.userData.pressed = false;
        });
    }
    
    simulateElevatorMovement() {
        if (this.moving) return;
        
        this.moving = true;
        console.log('Elevator moving...');
        
        // Play elevator movement sound
        if (window.game && window.game.audioManager) {
            window.game.audioManager.playSound('elevator_move', { loop: true });
        }
        
        // Simulate movement with camera shake
        const originalPosition = window.game.camera.position.clone();
        const shakeIntensity = 0.02;
        const shakeDuration = 3000; // 3 seconds
        const startTime = Date.now();
        
        const shake = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < shakeDuration) {
                window.game.camera.position.x = originalPosition.x + (Math.random() - 0.5) * shakeIntensity;
                window.game.camera.position.z = originalPosition.z + (Math.random() - 0.5) * shakeIntensity;
                requestAnimationFrame(shake);
            } else {
                window.game.camera.position.copy(originalPosition);
                this.moving = false;
                
                // Stop movement sound
                if (window.game && window.game.audioManager) {
                    window.game.audioManager.stopSound('elevator_move');
                }
            }
        };
        
        shake();
    }
    
    createFallbackScene() {
        console.log('Creating fallback elevator scene');
        this.createPlaceholderElevator();
        this.createButtonPanel();
        this.loaded = true;
    }
    
    update(deltaTime) {
        // Animate button lights
        this.buttons.forEach(button => {
            if (button.mesh.userData.pressed) {
                // Pulsing effect for pressed buttons
                const pulse = Math.sin(Date.now() * 0.005) * 0.3 + 0.7;
                button.mesh.material.color.setRGB(pulse, pulse * 0.3, pulse * 0.3);
            }
        });
        
        // Flicker elevator light occasionally
        if (Math.random() < 0.001) { // 0.1% chance per frame
            this.objects.forEach(obj => {
                if (obj.material && obj.material.emissive) {
                    obj.material.emissive.setHex(Math.random() > 0.5 ? 0x444444 : 0x222222);
                }
            });
        }
    }
    
    dispose() {
        this.objects.forEach(obj => {
            this.scene.remove(obj);
        });
        this.objects = [];
        this.buttons = [];
        this.buttonMeshes = [];
        this.loaded = false;
    }
}
