<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DESCENT - Elevator Game</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Creepster&family=Nosifer&family=Butcherman&family=Eater&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            cursor: none;
        }
        
        body {
            font-family: 'Butcherman', cursive;
            background: #000;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }
        
        /* Custom Cursor */
        .cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border: 2px solid #ff0000;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            mix-blend-mode: difference;
            transition: transform 0.1s ease;
        }
        
        /* Background Video */
        #backgroundVideo {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
            filter: brightness(0.3) contrast(1.2) saturate(0.8);
        }
        
        /* Dark Overlay */
        .dark-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.8) 100%);
            z-index: 2;
        }
        
        /* Main Container */
        .menu-container {
            position: relative;
            z-index: 10;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        /* Game Title */
        .game-title {
            font-family: 'Nosifer', cursive;
            font-size: clamp(4rem, 12vw, 10rem);
            color: #ffffff;
            text-shadow: 
                0 0 10px #ff0000,
                0 0 20px #ff0000,
                0 0 30px #ff0000,
                0 0 40px #ff0000,
                0 0 50px #ff0000;
            margin-bottom: 2rem;
            animation: titleFlicker 4s infinite, titleGlow 3s ease-in-out infinite alternate;
            letter-spacing: 0.2em;
            position: relative;
        }
        
        .game-title::before {
            content: 'DESCENT';
            position: absolute;
            top: 0;
            left: 0;
            color: #ff0000;
            opacity: 0;
            animation: glitchRed 0.3s infinite;
            transform: translateX(2px);
        }
        
        .game-title::after {
            content: 'DESCENT';
            position: absolute;
            top: 0;
            left: 0;
            color: #00ffff;
            opacity: 0;
            animation: glitchBlue 0.3s infinite;
            transform: translateX(-2px);
        }
        
        /* Subtitle */
        .subtitle {
            font-family: 'Butcherman', cursive;
            font-size: clamp(1rem, 3vw, 1.5rem);
            color: #cccccc;
            margin-bottom: 4rem;
            opacity: 0.8;
            animation: subtitleFlicker 6s infinite;
            letter-spacing: 0.1em;
        }
        
        /* Menu Buttons */
        .menu-buttons {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            align-items: center;
        }
        
        .menu-button {
            font-family: 'Butcherman', cursive;
            font-size: clamp(1.2rem, 3vw, 2rem);
            color: #cccccc;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #666666;
            padding: 1rem 3rem;
            text-transform: uppercase;
            letter-spacing: 0.2em;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: none;
            backdrop-filter: blur(5px);
        }
        
        .menu-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 0, 0, 0.3), transparent);
            transition: left 0.5s ease;
        }
        
        .menu-button:hover {
            color: #ffffff;
            border-color: #ff0000;
            box-shadow: 
                0 0 20px rgba(255, 0, 0, 0.5),
                inset 0 0 20px rgba(255, 0, 0, 0.1);
            transform: translateY(-2px);
            animation: buttonGlitch 0.1s infinite;
        }
        
        .menu-button:hover::before {
            left: 100%;
        }
        
        .menu-button:active {
            transform: translateY(0);
            color: #ff0000;
            animation: buttonFlash 0.2s ease;
        }
        
        /* Instructions Panel */
        .instructions-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 100;
            display: none;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .instructions-content {
            background: rgba(20, 0, 0, 0.9);
            border: 2px solid #ff0000;
            padding: 3rem;
            border-radius: 10px;
            color: #ffffff;
            font-family: 'Butcherman', cursive;
            max-width: 600px;
            text-align: left;
            position: relative;
            box-shadow: 
                0 0 30px rgba(255, 0, 0, 0.5),
                inset 0 0 30px rgba(255, 0, 0, 0.1);
        }
        
        .instructions-title {
            font-family: 'Eater', cursive;
            font-size: 2rem;
            color: #ff0000;
            text-align: center;
            margin-bottom: 2rem;
            animation: titleFlicker 2s infinite;
        }
        
        .ritual-sequence {
            font-size: 1.5rem;
            text-align: center;
            margin: 2rem 0;
            color: #ffff00;
            animation: sequenceGlow 2s ease-in-out infinite alternate;
        }
        
        .warning-text {
            color: #ff6666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin: 1rem 0;
        }
        
        .close-button {
            position: absolute;
            top: 10px;
            right: 20px;
            background: none;
            border: none;
            color: #ff0000;
            font-size: 2rem;
            cursor: none;
            animation: closeFlicker 3s infinite;
        }
        
        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .loading-text {
            font-family: 'Butcherman', cursive;
            color: #ff0000;
            font-size: 2rem;
            animation: loadingPulse 1.5s infinite;
        }
        
        .loading-bar {
            width: 300px;
            height: 4px;
            background: #333;
            margin-top: 2rem;
            overflow: hidden;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ff6666, #ff0000);
            width: 0%;
            animation: loadingProgress 3s ease-in-out;
        }
        
        /* Animations */
        @keyframes titleFlicker {
            0%, 100% { opacity: 1; }
            2% { opacity: 0.8; }
            4% { opacity: 1; }
            8% { opacity: 0.9; }
            10% { opacity: 1; }
            92% { opacity: 1; }
            94% { opacity: 0.7; }
            96% { opacity: 1; }
            98% { opacity: 0.9; }
        }
        
        @keyframes titleGlow {
            0% { text-shadow: 0 0 10px #ff0000, 0 0 20px #ff0000, 0 0 30px #ff0000; }
            100% { text-shadow: 0 0 20px #ff0000, 0 0 30px #ff0000, 0 0 40px #ff0000, 0 0 50px #ff0000; }
        }
        
        @keyframes glitchRed {
            0%, 90%, 100% { opacity: 0; }
            5% { opacity: 0.7; transform: translateX(2px) skew(2deg); }
        }
        
        @keyframes glitchBlue {
            0%, 90%, 100% { opacity: 0; }
            7% { opacity: 0.5; transform: translateX(-2px) skew(-1deg); }
        }
        
        @keyframes subtitleFlicker {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 0.4; }
        }
        
        @keyframes buttonGlitch {
            0%, 100% { transform: translateY(-2px); }
            50% { transform: translateY(-2px) translateX(1px); }
        }
        
        @keyframes buttonFlash {
            0% { background: rgba(255, 0, 0, 0.3); }
            100% { background: rgba(0, 0, 0, 0.7); }
        }
        
        @keyframes sequenceGlow {
            0% { text-shadow: 0 0 10px #ffff00; }
            100% { text-shadow: 0 0 20px #ffff00, 0 0 30px #ffff00; }
        }
        
        @keyframes closeFlicker {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes loadingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        @keyframes loadingProgress {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .menu-button {
                padding: 0.8rem 2rem;
                font-size: 1.2rem;
            }
            
            .instructions-content {
                margin: 1rem;
                padding: 2rem;
            }
        }
        
        /* Hidden class */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Custom Cursor -->
    <div class="cursor"></div>
    
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-text">LOADING NIGHTMARE...</div>
        <div class="loading-bar">
            <div class="loading-progress"></div>
        </div>
    </div>
    
    <!-- Background Video -->
    <video id="backgroundVideo" autoplay muted loop>
        <source src="GAME MENU/ASSETS/GAME MENU/GAME MENU BG .mp4" type="video/mp4">
    </video>
    
    <!-- Dark Overlay -->
    <div class="dark-overlay"></div>
    
    <!-- Main Menu -->
    <div class="menu-container" id="mainMenu">
        <h1 class="game-title">DESCENT</h1>
        <p class="subtitle">The Elevator Ritual</p>
        
        <div class="menu-buttons">
            <button class="menu-button" id="startButton">START RITUAL</button>
            <button class="menu-button" id="instructionsButton">INSTRUCTIONS</button>
            <button class="menu-button" id="settingsButton">SETTINGS</button>
            <button class="menu-button" id="quitButton">QUIT</button>
        </div>
    </div>
    
    <!-- Instructions Overlay -->
    <div class="instructions-overlay" id="instructionsOverlay">
        <div class="instructions-content">
            <button class="close-button" id="closeInstructions">&times;</button>
            <h2 class="instructions-title">THE RITUAL</h2>
            
            <div class="ritual-sequence">4 → 2 → 6 → 2 → 10 → 5</div>
            
            <div class="warning-text">
                <p><strong>RULES:</strong></p>
                <p>• DO NOT LEAVE THE ELEVATOR</p>
                <p>• DO NOT LOOK AT HER</p>
                <p>• DO NOT SPEAK</p>
                <p>• PRESS THE BUTTONS IN EXACT ORDER</p>
                <br>
                <p><strong>WARNING:</strong></p>
                <p>Failure to follow the ritual will result in consequences beyond your imagination. The Other World does not forgive mistakes.</p>
            </div>
        </div>
    </div>
    
    <!-- Audio Elements -->
    <audio id="ambientAudio" loop>
        <source src="GAME MENU/ASSETS/Sound FX/Neon tube light flickering sound effect - fluorescent bulb - horror movie.mp3" type="audio/mpeg">
    </audio>
    
    <audio id="growlAudio">
        <source src="GAME MENU/Growl.mp3" type="audio/mpeg">
    </audio>
    
    <script>
        // Custom cursor
        const cursor = document.querySelector('.cursor');
        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });
        
        // Audio elements
        const ambientAudio = document.getElementById('ambientAudio');
        const growlAudio = document.getElementById('growlAudio');
        const backgroundVideo = document.getElementById('backgroundVideo');
        
        // Menu elements
        const loadingScreen = document.getElementById('loadingScreen');
        const mainMenu = document.getElementById('mainMenu');
        const instructionsOverlay = document.getElementById('instructionsOverlay');
        
        // Buttons
        const startButton = document.getElementById('startButton');
        const instructionsButton = document.getElementById('instructionsButton');
        const settingsButton = document.getElementById('settingsButton');
        const quitButton = document.getElementById('quitButton');
        const closeInstructions = document.getElementById('closeInstructions');
        
        // Initialize
        window.addEventListener('load', () => {
            // Hide loading screen after delay
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                startAmbientAudio();
            }, 3000);
        });
        
        function startAmbientAudio() {
            ambientAudio.volume = 0.3;
            ambientAudio.play().catch(e => {
                console.log('Audio autoplay blocked');
            });
        }
        
        function playHoverSound() {
            // Create a brief static sound effect
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(200 + Math.random() * 100, audioContext.currentTime);
            oscillator.type = 'sawtooth';
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }
        
        function playClickSound() {
            growlAudio.volume = 0.5;
            growlAudio.currentTime = 0;
            growlAudio.play().catch(e => console.log('Click sound failed'));
        }
        
        // Button event listeners
        startButton.addEventListener('mouseenter', playHoverSound);
        instructionsButton.addEventListener('mouseenter', playHoverSound);
        settingsButton.addEventListener('mouseenter', playHoverSound);
        quitButton.addEventListener('mouseenter', playHoverSound);
        
        startButton.addEventListener('click', () => {
            playClickSound();
            setTimeout(() => {
                // Transition to game
                document.body.style.transition = 'opacity 1s ease';
                document.body.style.opacity = '0';
                setTimeout(() => {
                    window.location.href = 'index.html'; // Link to your main game
                }, 1000);
            }, 500);
        });
        
        instructionsButton.addEventListener('click', () => {
            playClickSound();
            instructionsOverlay.style.display = 'flex';
            setTimeout(() => {
                instructionsOverlay.style.opacity = '1';
            }, 10);
        });
        
        settingsButton.addEventListener('click', () => {
            playClickSound();
            alert('Settings menu coming soon...');
        });
        
        quitButton.addEventListener('click', () => {
            playClickSound();
            // Fade to black and close
            document.body.style.transition = 'opacity 2s ease';
            document.body.style.opacity = '0';
            setTimeout(() => {
                window.close();
            }, 2000);
        });
        
        closeInstructions.addEventListener('click', () => {
            instructionsOverlay.style.opacity = '0';
            setTimeout(() => {
                instructionsOverlay.style.display = 'none';
            }, 300);
        });
        
        // Escape key to close instructions
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                instructionsOverlay.style.opacity = '0';
                setTimeout(() => {
                    instructionsOverlay.style.display = 'none';
                }, 300);
            }
        });
        
        // Random glitch effects
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance
                const title = document.querySelector('.game-title');
                title.style.transform = `translateX(${(Math.random() - 0.5) * 10}px)`;
                setTimeout(() => {
                    title.style.transform = 'translateX(0)';
                }, 100);
            }
        }, 2000);
        
        // Video error handling
        backgroundVideo.addEventListener('error', () => {
            console.log('Background video failed to load');
            document.body.style.background = 'linear-gradient(45deg, #000000, #1a0000, #000000)';
        });
        
        // Ensure video plays
        backgroundVideo.addEventListener('canplay', () => {
            backgroundVideo.play().catch(e => {
                console.log('Video autoplay blocked');
            });
        });
    </script>
</body>
</html>
