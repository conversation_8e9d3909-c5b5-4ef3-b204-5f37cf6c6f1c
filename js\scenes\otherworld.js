// DESCENT - Other World Scene
class OtherWorldScene {
    constructor(scene) {
        this.scene = scene;
        this.loader = new THREE.GLTFLoader();
        
        this.objects = [];
        this.loaded = false;
        
        // Other World atmosphere settings
        this.redGlowIntensity = 0.5;
        this.distortionAmount = 0.02;
        this.time = 0;
        
        // Animation mixers
        this.mixers = [];
    }
    
    async load() {
        console.log('Loading Other World scene...');
        
        try {
            // Try to load other world model
            await this.loadOtherWorldModel();
            
            // Create placeholder if model fails
            this.createPlaceholderOtherWorld();
            
            // Set up Other World atmosphere
            this.createOtherWorldAtmosphere();
            
            // Configure lighting for Other World
            if (window.game && window.game.lights) {
                window.game.lights.setOtherWorldLighting();
            }
            
            // Start Other World audio
            if (window.game && window.game.audioManager) {
                window.game.audioManager.startOtherWorldAudio();
                window.game.audioManager.startHeartbeat();
            }
            
            this.loaded = true;
            console.log('Other World scene loaded successfully');
            
        } catch (error) {
            console.error('Error loading Other World scene:', error);
            this.createFallbackScene();
        }
    }
    
    async loadOtherWorldModel() {
        return new Promise((resolve, reject) => {
            this.loader.load(
                'assets/models/otherworld.glb',
                (gltf) => {
                    console.log('Other World model loaded');
                    
                    const model = gltf.scene;
                    model.position.set(0, 0, 0);
                    model.scale.set(1, 1, 1);
                    
                    // Configure model for Other World atmosphere
                    model.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                            child.userData.collidable = true;
                            
                            // Desaturate and darken materials
                            if (child.material) {
                                child.material.roughness = 0.9;
                                child.material.metalness = 0.1;
                                
                                // Add red tint
                                if (child.material.color) {
                                    const color = child.material.color;
                                    color.r = Math.min(1, color.r * 1.2);
                                    color.g *= 0.6;
                                    color.b *= 0.6;
                                }
                            }
                        }
                    });
                    
                    this.scene.add(model);
                    this.objects.push(model);
                    
                    // Set up animations if any
                    if (gltf.animations && gltf.animations.length > 0) {
                        const mixer = new THREE.AnimationMixer(model);
                        gltf.animations.forEach((clip) => {
                            mixer.clipAction(clip).play();
                        });
                        this.mixers.push(mixer);
                    }
                    
                    resolve();
                },
                (progress) => {
                    console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
                },
                (error) => {
                    console.warn('Failed to load Other World model, using placeholder');
                    resolve(); // Don't reject, use placeholder instead
                }
            );
        });
    }
    
    createPlaceholderOtherWorld() {
        // Create a dark, twisted version of the lobby
        
        // Floor (darker, with red tint)
        const floorGeometry = new THREE.PlaneGeometry(20, 50); // Longer hallway
        const floorMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x331111,
            roughness: 0.9
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = 0;
        floor.receiveShadow = true;
        this.scene.add(floor);
        this.objects.push(floor);
        
        // Walls (twisted and dark)
        this.createTwistedWalls();
        
        // Distorted furniture
        this.createDistortedFurniture();
        
        // Ominous red glow in the distance
        this.createDistantRedGlow();
        
        // Floating debris
        this.createFloatingDebris();
    }
    
    createTwistedWalls() {
        const wallHeight = 4;
        const wallLength = 50;
        const wallMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x220000,
            roughness: 0.95
        });
        
        // Left wall (slightly curved)
        const leftWallGeometry = new THREE.PlaneGeometry(wallLength, wallHeight);
        const leftWall = new THREE.Mesh(leftWallGeometry, wallMaterial);
        leftWall.rotation.y = Math.PI / 2;
        leftWall.position.set(-10, wallHeight / 2, -15);
        
        // Add slight curve distortion
        const leftWallVertices = leftWall.geometry.attributes.position.array;
        for (let i = 0; i < leftWallVertices.length; i += 3) {
            leftWallVertices[i] += Math.sin(leftWallVertices[i + 2] * 0.1) * 0.3;
        }
        leftWall.geometry.attributes.position.needsUpdate = true;
        
        leftWall.castShadow = true;
        leftWall.receiveShadow = true;
        leftWall.userData.collidable = true;
        this.scene.add(leftWall);
        this.objects.push(leftWall);
        
        // Right wall (slightly curved)
        const rightWallGeometry = new THREE.PlaneGeometry(wallLength, wallHeight);
        const rightWall = new THREE.Mesh(rightWallGeometry, wallMaterial);
        rightWall.rotation.y = -Math.PI / 2;
        rightWall.position.set(10, wallHeight / 2, -15);
        
        // Add slight curve distortion
        const rightWallVertices = rightWall.geometry.attributes.position.array;
        for (let i = 0; i < rightWallVertices.length; i += 3) {
            rightWallVertices[i] += Math.sin(rightWallVertices[i + 2] * 0.1) * -0.3;
        }
        rightWall.geometry.attributes.position.needsUpdate = true;
        
        rightWall.castShadow = true;
        rightWall.receiveShadow = true;
        rightWall.userData.collidable = true;
        this.scene.add(rightWall);
        this.objects.push(rightWall);
        
        // Ceiling (lower and oppressive)
        const ceilingGeometry = new THREE.PlaneGeometry(20, wallLength);
        const ceilingMaterial = new THREE.MeshLambertMaterial({ color: 0x110000 });
        const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
        ceiling.rotation.x = Math.PI / 2;
        ceiling.position.set(0, 3.5, -15); // Lower ceiling
        ceiling.receiveShadow = true;
        this.scene.add(ceiling);
        this.objects.push(ceiling);
    }
    
    createDistortedFurniture() {
        // Twisted chairs and furniture
        this.createTwistedChair(-7, 0, -5);
        this.createTwistedChair(6, 0, -10);
        this.createTwistedChair(-4, 0, -20);
        
        // Broken reception desk
        this.createBrokenDesk();
        
        // Scattered, floating objects
        this.createScatteredObjects();
    }
    
    createTwistedChair(x, y, z) {
        const chairGroup = new THREE.Group();
        const chairMaterial = new THREE.MeshLambertMaterial({ color: 0x441111 });
        
        // Distorted seat
        const seatGeometry = new THREE.BoxGeometry(0.8, 0.1, 0.8);
        const seat = new THREE.Mesh(seatGeometry, chairMaterial);
        seat.position.y = 0.5;
        seat.rotation.z = (Math.random() - 0.5) * 0.3; // Slight tilt
        chairGroup.add(seat);
        
        // Twisted backrest
        const backGeometry = new THREE.BoxGeometry(0.8, 1, 0.1);
        const back = new THREE.Mesh(backGeometry, chairMaterial);
        back.position.set(0, 1, -0.35);
        back.rotation.x = (Math.random() - 0.5) * 0.5; // Twisted
        chairGroup.add(back);
        
        // Broken legs
        const legGeometry = new THREE.BoxGeometry(0.05, 0.5, 0.05);
        const legPositions = [
            [-0.35, 0.25, -0.35],
            [0.35, 0.25, -0.35],
            [-0.35, 0.25, 0.35]
            // Missing one leg
        ];
        
        legPositions.forEach(pos => {
            const leg = new THREE.Mesh(legGeometry, chairMaterial);
            leg.position.set(pos[0], pos[1], pos[2]);
            leg.rotation.z = (Math.random() - 0.5) * 0.2;
            chairGroup.add(leg);
        });
        
        chairGroup.position.set(x, y, z);
        chairGroup.rotation.y = Math.random() * Math.PI * 2;
        
        chairGroup.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                child.userData.collidable = true;
            }
        });
        
        this.scene.add(chairGroup);
        this.objects.push(chairGroup);
    }
    
    createBrokenDesk() {
        const deskGroup = new THREE.Group();
        const deskMaterial = new THREE.MeshLambertMaterial({ color: 0x332211 });
        
        // Broken desk top (split in half)
        const deskPart1 = new THREE.BoxGeometry(1.5, 1, 1.5);
        const desk1 = new THREE.Mesh(deskPart1, deskMaterial);
        desk1.position.set(-4, 0.5, -7);
        desk1.rotation.y = 0.2;
        deskGroup.add(desk1);
        
        const deskPart2 = new THREE.BoxGeometry(1.5, 1, 1.5);
        const desk2 = new THREE.Mesh(deskPart2, deskMaterial);
        desk2.position.set(-2.5, 0.3, -7.5);
        desk2.rotation.y = -0.3;
        desk2.rotation.z = 0.1;
        deskGroup.add(desk2);
        
        deskGroup.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                child.userData.collidable = true;
            }
        });
        
        this.scene.add(deskGroup);
        this.objects.push(deskGroup);
    }
    
    createScatteredObjects() {
        // Floating, rotating debris
        for (let i = 0; i < 15; i++) {
            const debrisGeometry = new THREE.BoxGeometry(
                0.1 + Math.random() * 0.4,
                0.1 + Math.random() * 0.4,
                0.1 + Math.random() * 0.4
            );
            const debrisMaterial = new THREE.MeshLambertMaterial({ 
                color: new THREE.Color().setHSL(0, 0.3, 0.1 + Math.random() * 0.2)
            });
            const debris = new THREE.Mesh(debrisGeometry, debrisMaterial);
            
            debris.position.set(
                (Math.random() - 0.5) * 18,
                0.5 + Math.random() * 2,
                -Math.random() * 40
            );
            debris.rotation.set(
                Math.random() * Math.PI,
                Math.random() * Math.PI,
                Math.random() * Math.PI
            );
            
            debris.castShadow = true;
            debris.receiveShadow = true;
            debris.userData.collidable = false;
            debris.userData.floating = true;
            debris.userData.rotationSpeed = (Math.random() - 0.5) * 2;
            debris.userData.floatSpeed = (Math.random() - 0.5) * 0.5;
            
            this.scene.add(debris);
            this.objects.push(debris);
        }
    }
    
    createDistantRedGlow() {
        // Mysterious red light at the end of the hallway
        const redGlow = new THREE.PointLight(0xff3333, 2, 100, 2);
        redGlow.position.set(0, 2, -40);
        this.scene.add(redGlow);
        this.objects.push(redGlow);
        
        // Red glow sphere (visible light source)
        const glowGeometry = new THREE.SphereGeometry(0.5, 16, 16);
        const glowMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff3333,
            transparent: true,
            opacity: 0.7
        });
        const glowSphere = new THREE.Mesh(glowGeometry, glowMaterial);
        glowSphere.position.copy(redGlow.position);
        this.scene.add(glowSphere);
        this.objects.push(glowSphere);
    }
    
    createFloatingDebris() {
        // Particle-like floating debris
        const particleCount = 30;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 20;     // x
            positions[i + 1] = Math.random() * 4;          // y
            positions[i + 2] = -Math.random() * 40;        // z
        }
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xff6666,
            size: 0.05,
            transparent: true,
            opacity: 0.6
        });
        
        const debrisSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(debrisSystem);
        this.objects.push(debrisSystem);
    }
    
    createOtherWorldAtmosphere() {
        // Add fog for atmosphere
        this.scene.fog = new THREE.Fog(0x220000, 5, 30);
        
        // Create atmospheric effects
        this.createAtmosphericEffects();
    }
    
    createAtmosphericEffects() {
        // Subtle screen distortion effect (would need post-processing in full implementation)
        console.log('Other World atmospheric effects active');
    }
    
    createFallbackScene() {
        console.log('Creating fallback Other World scene');
        this.createPlaceholderOtherWorld();
        this.createOtherWorldAtmosphere();
        this.loaded = true;
    }
    
    update(deltaTime) {
        this.time += deltaTime;
        
        // Update animations
        this.mixers.forEach(mixer => {
            mixer.update(deltaTime);
        });
        
        // Animate floating objects
        this.objects.forEach(obj => {
            if (obj.userData.floating) {
                obj.rotation.x += obj.userData.rotationSpeed * deltaTime;
                obj.rotation.y += obj.userData.rotationSpeed * deltaTime * 0.7;
                obj.position.y += Math.sin(this.time * 2 + obj.position.x) * obj.userData.floatSpeed * deltaTime;
            }
            
            // Animate particle systems
            if (obj.isPoints) {
                obj.rotation.y += deltaTime * 0.1;
                
                const positions = obj.geometry.attributes.position.array;
                for (let i = 0; i < positions.length; i += 3) {
                    positions[i + 1] += deltaTime * 0.2; // Slow upward drift
                    if (positions[i + 1] > 4) {
                        positions[i + 1] = 0; // Reset to bottom
                    }
                    
                    // Add slight horizontal drift
                    positions[i] += Math.sin(this.time + i) * deltaTime * 0.1;
                    positions[i + 2] += Math.cos(this.time + i) * deltaTime * 0.05;
                }
                obj.geometry.attributes.position.needsUpdate = true;
            }
        });
        
        // Pulse the red glow
        this.objects.forEach(obj => {
            if (obj.isPointLight && obj.color.r > 0.5) {
                obj.intensity = this.redGlowIntensity + Math.sin(this.time * 2) * 0.3;
            }
            
            // Pulse red glow sphere
            if (obj.isMesh && obj.material.color && obj.material.color.r > 0.5) {
                obj.material.opacity = 0.7 + Math.sin(this.time * 2) * 0.2;
            }
        });
    }
    
    dispose() {
        this.objects.forEach(obj => {
            this.scene.remove(obj);
        });
        this.objects = [];
        this.mixers = [];
        this.loaded = false;
        
        // Stop heartbeat
        if (window.game && window.game.audioManager) {
            window.game.audioManager.stopHeartbeat();
        }
    }
}
