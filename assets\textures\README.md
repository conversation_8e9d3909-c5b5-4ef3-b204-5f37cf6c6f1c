# Textures Directory

Place your texture files here:

## Texture Types:
- Diffuse/Albedo maps
- Normal maps
- Roughness maps
- Metallic maps
- Ambient Occlusion maps
- Emissive maps

## Recommended Formats:
- JPG for diffuse/albedo (smaller file size)
- PNG for normal maps, roughness, metallic (better quality)
- WebP for modern browser support (smaller size)

## Texture Guidelines:
- Power-of-2 dimensions (256x256, 512x512, 1024x1024, etc.)
- Keep file sizes reasonable for web loading
- Use compression where appropriate
- Consider mip-mapping for distance textures

## PBR Workflow:
The game uses Physically Based Rendering (PBR) materials:
- Albedo: Base color without lighting information
- Normal: Surface detail and bumps
- Roughness: Surface smoothness (0 = mirror, 1 = rough)
- Metallic: Metallic vs non-metallic (0 or 1, avoid in-between)
- AO: Ambient occlusion for contact shadows

## Placeholder Textures:
If textures are not available, the game will use procedurally generated textures or solid colors.
