// DESCENT - Audio Management System
class AudioManager {
    constructor() {
        this.sounds = {};
        this.ambientSounds = {};
        this.musicVolume = 0.7;
        this.sfxVolume = 0.8;
        this.ambientVolume = 0.5;
        this.masterVolume = 1.0;
        
        this.currentAmbient = null;
        this.audioContext = null;
        this.initialized = false;
    }
    
    init() {
        // Initialize Howler.js audio
        this.loadSounds();
        this.loadAmbientSounds();
        
        console.log('Audio Manager initialized');
        this.initialized = true;
    }
    
    loadSounds() {
        // Menu and UI sounds
        this.sounds.static_hover = new Howl({
            src: ['assets/audio/static_hover.wav'],
            volume: this.sfxVolume * 0.3,
            preload: true
        });
        
        this.sounds.elevator_ding = new Howl({
            src: ['assets/audio/elevator_ding.wav'],
            volume: this.sfxVolume * 0.5,
            preload: true
        });
        
        this.sounds.digital_distortion = new Howl({
            src: ['assets/audio/digital_distortion.wav'],
            volume: this.sfxVolume * 0.2,
            preload: true
        });
        
        // Game sounds
        this.sounds.jumpscare = new Howl({
            src: ['assets/audio/jumpscare.wav'],
            volume: this.sfxVolume * 0.9,
            preload: true
        });
        
        this.sounds.door_open = new Howl({
            src: ['assets/audio/door_open.wav'],
            volume: this.sfxVolume * 0.6,
            preload: true
        });
        
        this.sounds.door_close = new Howl({
            src: ['assets/audio/door_close.wav'],
            volume: this.sfxVolume * 0.6,
            preload: true
        });
        
        this.sounds.elevator_move = new Howl({
            src: ['assets/audio/elevator_move.wav'],
            volume: this.sfxVolume * 0.4,
            preload: true
        });
        
        this.sounds.footstep = new Howl({
            src: ['assets/audio/footstep.wav'],
            volume: this.sfxVolume * 0.3,
            preload: true
        });
        
        this.sounds.heartbeat = new Howl({
            src: ['assets/audio/heartbeat.wav'],
            volume: this.sfxVolume * 0.5,
            loop: true,
            preload: true
        });
    }
    
    loadAmbientSounds() {
        // Ambient background loops
        this.ambientSounds.lobby_ambient = new Howl({
            src: ['assets/audio/lobby_ambient.ogg'],
            volume: this.ambientVolume * 0.6,
            loop: true,
            preload: true
        });
        
        this.ambientSounds.elevator_hum = new Howl({
            src: ['assets/audio/elevator_hum.ogg'],
            volume: this.ambientVolume * 0.7,
            loop: true,
            preload: true
        });
        
        this.ambientSounds.otherworld_ambient = new Howl({
            src: ['assets/audio/otherworld_ambient.ogg'],
            volume: this.ambientVolume * 0.8,
            loop: true,
            preload: true
        });
        
        this.ambientSounds.light_flicker = new Howl({
            src: ['assets/audio/light_flicker.ogg'],
            volume: this.ambientVolume * 0.4,
            loop: true,
            preload: true
        });
        
        this.ambientSounds.menu_idle = new Howl({
            src: ['assets/audio/menu_idle.ogg'],
            volume: this.ambientVolume * 0.3,
            loop: true,
            preload: true
        });
    }
    
    playSound(soundName, options = {}) {
        if (!this.initialized) {
            console.warn('Audio manager not initialized');
            return;
        }
        
        const sound = this.sounds[soundName];
        if (!sound) {
            console.warn(`Sound '${soundName}' not found`);
            return;
        }
        
        // Apply options
        if (options.volume !== undefined) {
            sound.volume(options.volume * this.sfxVolume * this.masterVolume);
        }
        
        if (options.rate !== undefined) {
            sound.rate(options.rate);
        }
        
        if (options.loop !== undefined) {
            sound.loop(options.loop);
        }
        
        // Play the sound
        const id = sound.play();
        
        // Return sound ID for potential stopping
        return id;
    }
    
    stopSound(soundName, id = null) {
        const sound = this.sounds[soundName];
        if (!sound) {
            console.warn(`Sound '${soundName}' not found`);
            return;
        }
        
        if (id) {
            sound.stop(id);
        } else {
            sound.stop();
        }
    }
    
    playAmbient(ambientName, fadeIn = true) {
        if (!this.initialized) {
            console.warn('Audio manager not initialized');
            return;
        }
        
        // Stop current ambient
        this.stopCurrentAmbient();
        
        const ambient = this.ambientSounds[ambientName];
        if (!ambient) {
            console.warn(`Ambient sound '${ambientName}' not found`);
            return;
        }
        
        this.currentAmbient = ambient;
        
        if (fadeIn) {
            ambient.volume(0);
            const id = ambient.play();
            ambient.fade(0, this.ambientVolume * this.masterVolume, 2000, id);
        } else {
            ambient.volume(this.ambientVolume * this.masterVolume);
            ambient.play();
        }
        
        console.log(`Playing ambient: ${ambientName}`);
    }
    
    stopCurrentAmbient(fadeOut = true) {
        if (!this.currentAmbient) return;
        
        if (fadeOut) {
            this.currentAmbient.fade(
                this.currentAmbient.volume(), 
                0, 
                1000
            );
            setTimeout(() => {
                if (this.currentAmbient) {
                    this.currentAmbient.stop();
                    this.currentAmbient = null;
                }
            }, 1000);
        } else {
            this.currentAmbient.stop();
            this.currentAmbient = null;
        }
    }
    
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        
        // Update all currently playing sounds
        Object.values(this.sounds).forEach(sound => {
            if (sound.playing()) {
                sound.volume(sound._volume * this.masterVolume);
            }
        });
        
        Object.values(this.ambientSounds).forEach(sound => {
            if (sound.playing()) {
                sound.volume(sound._volume * this.masterVolume);
            }
        });
    }
    
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
    }
    
    setAmbientVolume(volume) {
        this.ambientVolume = Math.max(0, Math.min(1, volume));
        
        if (this.currentAmbient && this.currentAmbient.playing()) {
            this.currentAmbient.volume(this.ambientVolume * this.masterVolume);
        }
    }
    
    // Scene-specific audio methods
    startGameAudio() {
        this.playAmbient('lobby_ambient');
        this.playAmbient('light_flicker');
    }
    
    startLobbyAudio() {
        this.playAmbient('lobby_ambient');
    }
    
    startElevatorAudio() {
        this.playAmbient('elevator_hum');
    }
    
    startOtherWorldAudio() {
        this.playAmbient('otherworld_ambient');
    }
    
    // Special effects
    playRandomFlickerSound() {
        if (Math.random() < 0.3) { // 30% chance
            this.playSound('light_flicker', { 
                volume: 0.2 + Math.random() * 0.3,
                rate: 0.8 + Math.random() * 0.4
            });
        }
    }
    
    playFootstep() {
        this.playSound('footstep', {
            volume: 0.2 + Math.random() * 0.2,
            rate: 0.9 + Math.random() * 0.2
        });
    }
    
    startHeartbeat() {
        return this.playSound('heartbeat', { loop: true });
    }
    
    stopHeartbeat() {
        this.stopSound('heartbeat');
    }
    
    // Utility methods
    muteAll() {
        Howler.mute(true);
    }
    
    unmuteAll() {
        Howler.mute(false);
    }
    
    pauseAll() {
        Object.values(this.sounds).forEach(sound => sound.pause());
        Object.values(this.ambientSounds).forEach(sound => sound.pause());
    }
    
    resumeAll() {
        Object.values(this.sounds).forEach(sound => {
            if (sound._paused) sound.play();
        });
        Object.values(this.ambientSounds).forEach(sound => {
            if (sound._paused) sound.play();
        });
    }
}
