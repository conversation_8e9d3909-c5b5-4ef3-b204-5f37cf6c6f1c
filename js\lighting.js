// DESCENT - Dynamic Lighting System
class LightingSystem {
    constructor(scene) {
        this.scene = scene;
        this.lights = [];
        this.flickerLights = [];
        this.time = 0;
        
        // Flicker settings
        this.flickerIntensity = 0.3;
        this.flickerSpeed = 2.0;
        this.randomFlickerChance = 0.02; // 2% chance per frame
    }
    
    init() {
        this.createAmbientLighting();
        this.createMainLights();
        this.createFlickerLights();
        
        console.log('Lighting system initialized');
    }
    
    createAmbientLighting() {
        // Very dim ambient light for basic visibility
        const ambientLight = new THREE.AmbientLight(0x404040, 0.2);
        this.scene.add(ambientLight);
        this.lights.push(ambientLight);
    }
    
    createMainLights() {
        // Main overhead light (flickering)
        const mainLight = new THREE.SpotLight(0xffffff, 1.0, 30, Math.PI / 6, 0.5, 2);
        mainLight.position.set(0, 8, 0);
        mainLight.target.position.set(0, 0, 0);
        mainLight.castShadow = true;
        
        // Shadow settings
        mainLight.shadow.mapSize.width = 1024;
        mainLight.shadow.mapSize.height = 1024;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 50;
        
        this.scene.add(mainLight);
        this.scene.add(mainLight.target);
        this.lights.push(mainLight);
        this.flickerLights.push({
            light: mainLight,
            baseIntensity: 1.0,
            flickerAmount: 0.4,
            flickerSpeed: 1.5,
            phase: 0
        });
        
        // Secondary light (elevator area)
        const elevatorLight = new THREE.SpotLight(0xffffcc, 0.8, 25, Math.PI / 4, 0.3, 1.5);
        elevatorLight.position.set(0, 6, -8);
        elevatorLight.target.position.set(0, 0, -8);
        elevatorLight.castShadow = true;
        
        elevatorLight.shadow.mapSize.width = 512;
        elevatorLight.shadow.mapSize.height = 512;
        
        this.scene.add(elevatorLight);
        this.scene.add(elevatorLight.target);
        this.lights.push(elevatorLight);
        this.flickerLights.push({
            light: elevatorLight,
            baseIntensity: 0.8,
            flickerAmount: 0.3,
            flickerSpeed: 2.0,
            phase: Math.PI / 3
        });
        
        // Corner accent lights
        this.createCornerLights();
    }
    
    createCornerLights() {
        const cornerPositions = [
            { x: -10, z: -10 },
            { x: 10, z: -10 },
            { x: -10, z: 10 },
            { x: 10, z: 10 }
        ];
        
        cornerPositions.forEach((pos, index) => {
            const cornerLight = new THREE.PointLight(0xff9966, 0.3, 15, 2);
            cornerLight.position.set(pos.x, 3, pos.z);
            
            this.scene.add(cornerLight);
            this.lights.push(cornerLight);
            
            // Some corner lights flicker
            if (index % 2 === 0) {
                this.flickerLights.push({
                    light: cornerLight,
                    baseIntensity: 0.3,
                    flickerAmount: 0.2,
                    flickerSpeed: 3.0 + Math.random() * 2.0,
                    phase: Math.random() * Math.PI * 2
                });
            }
        });
    }
    
    createFlickerLights() {
        // Additional atmospheric flickering lights
        const flickerPositions = [
            { x: -5, y: 4, z: 0 },
            { x: 5, y: 4, z: 0 },
            { x: 0, y: 4, z: 5 }
        ];
        
        flickerPositions.forEach((pos, index) => {
            const flickerLight = new THREE.PointLight(0xffffff, 0.5, 12, 1.5);
            flickerLight.position.set(pos.x, pos.y, pos.z);
            
            this.scene.add(flickerLight);
            this.lights.push(flickerLight);
            this.flickerLights.push({
                light: flickerLight,
                baseIntensity: 0.5,
                flickerAmount: 0.4,
                flickerSpeed: 4.0 + Math.random() * 3.0,
                phase: Math.random() * Math.PI * 2,
                randomFlicker: true
            });
        });
    }
    
    update(deltaTime) {
        this.time += deltaTime;
        
        // Update flickering lights
        this.flickerLights.forEach(flickerData => {
            this.updateFlickerLight(flickerData, deltaTime);
        });
        
        // Random dramatic flickers
        if (Math.random() < this.randomFlickerChance) {
            this.triggerRandomFlicker();
        }
    }
    
    updateFlickerLight(flickerData, deltaTime) {
        const { light, baseIntensity, flickerAmount, flickerSpeed, phase, randomFlicker } = flickerData;
        
        // Base flicker using sine wave
        let flickerValue = Math.sin(this.time * flickerSpeed + phase) * 0.5 + 0.5;
        
        // Add random noise for more realistic flicker
        if (randomFlicker) {
            flickerValue += (Math.random() - 0.5) * 0.3;
        }
        
        // Apply flicker to intensity
        const intensity = baseIntensity + (flickerValue - 0.5) * flickerAmount;
        light.intensity = Math.max(0, intensity);
        
        // Occasionally change color temperature for realism
        if (Math.random() < 0.01) { // 1% chance per frame
            const colorTemp = 0.9 + Math.random() * 0.2; // 0.9 to 1.1
            light.color.setRGB(colorTemp, colorTemp * 0.95, colorTemp * 0.9);
        }
    }
    
    triggerRandomFlicker() {
        // Pick a random light to flicker dramatically
        if (this.flickerLights.length === 0) return;
        
        const randomLight = this.flickerLights[Math.floor(Math.random() * this.flickerLights.length)];
        const originalIntensity = randomLight.light.intensity;
        
        // Dramatic flicker sequence
        const flickerSequence = [0.1, 0.8, 0.2, 1.0, 0.0, 0.6, originalIntensity];
        let step = 0;
        
        const flickerInterval = setInterval(() => {
            if (step < flickerSequence.length) {
                randomLight.light.intensity = flickerSequence[step] * randomLight.baseIntensity;
                step++;
            } else {
                clearInterval(flickerInterval);
                randomLight.light.intensity = originalIntensity;
            }
        }, 100);
        
        // Play flicker sound effect
        if (window.game && window.game.audioManager) {
            window.game.audioManager.playRandomFlickerSound();
        }
    }
    
    // Scene-specific lighting configurations
    setLobbyLighting() {
        // Dim, abandoned hotel lobby lighting
        this.flickerLights.forEach(flickerData => {
            flickerData.baseIntensity *= 0.7; // Reduce overall brightness
            flickerData.flickerAmount *= 1.5; // Increase flicker intensity
        });
        
        // Add more dramatic color variation
        this.lights.forEach(light => {
            if (light.isSpotLight || light.isPointLight) {
                light.color.setRGB(1.0, 0.95, 0.8); // Warm, old bulb color
            }
        });
    }
    
    setElevatorLighting() {
        // Harsh, fluorescent elevator lighting
        this.lights.forEach(light => {
            if (light.isSpotLight || light.isPointLight) {
                light.color.setRGB(1.0, 1.0, 0.95); // Cool white
                light.intensity *= 1.2; // Brighter
            }
        });
        
        // Reduce flicker for elevator (more stable)
        this.flickerLights.forEach(flickerData => {
            flickerData.flickerAmount *= 0.3;
            flickerData.flickerSpeed *= 0.5;
        });
    }
    
    setOtherWorldLighting() {
        // Dark, ominous other world lighting
        this.lights.forEach(light => {
            if (light.isSpotLight || light.isPointLight) {
                light.color.setRGB(1.0, 0.7, 0.7); // Red tint
                light.intensity *= 0.4; // Much dimmer
            }
        });
        
        // Increase flicker dramatically
        this.flickerLights.forEach(flickerData => {
            flickerData.flickerAmount *= 2.0;
            flickerData.flickerSpeed *= 1.5;
        });
        
        // Add red glow in the distance
        this.addDistantRedGlow();
    }
    
    addDistantRedGlow() {
        const redGlow = new THREE.PointLight(0xff3333, 0.5, 50, 2);
        redGlow.position.set(0, 2, -30);
        
        this.scene.add(redGlow);
        this.lights.push(redGlow);
        
        // Pulsing red glow
        this.flickerLights.push({
            light: redGlow,
            baseIntensity: 0.5,
            flickerAmount: 0.3,
            flickerSpeed: 0.5,
            phase: 0
        });
    }
    
    // Utility methods
    setOverallBrightness(multiplier) {
        this.lights.forEach(light => {
            if (light.isSpotLight || light.isPointLight) {
                light.intensity *= multiplier;
            }
        });
        
        this.flickerLights.forEach(flickerData => {
            flickerData.baseIntensity *= multiplier;
        });
    }
    
    enableShadows(enable = true) {
        this.lights.forEach(light => {
            if (light.isSpotLight || light.isDirectionalLight) {
                light.castShadow = enable;
            }
        });
    }
    
    dispose() {
        this.lights.forEach(light => {
            this.scene.remove(light);
        });
        this.lights = [];
        this.flickerLights = [];
    }
}
