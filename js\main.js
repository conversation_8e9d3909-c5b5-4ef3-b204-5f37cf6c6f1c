// DESCENT - Main Game Engine
class DescentGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentScene = 'menu';
        this.gameState = {
            elevatorSequence: [],
            correctSequence: [4, 2, 6, 2, 10, 5],
            inElevator: false,
            canInteract: false,
            nearElevator: false
        };
        
        this.clock = new THREE.Clock();
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        this.init();
    }
    
    init() {
        this.setupRenderer();
        this.setupCamera();
        this.setupScene();
        this.setupLighting();
        this.setupControls();
        this.setupEventListeners();
        this.setupAudio();
        
        // Start render loop
        this.animate();
        
        console.log('DESCENT Game Engine initialized');
    }
    
    setupRenderer() {
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: false
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 0.8;
        
        // Set dark background for horror atmosphere
        this.renderer.setClearColor(0x000000, 1);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 1.7, 5); // Eye level height
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x000000, 10, 50);
        
        // Add basic floor for collision
        const floorGeometry = new THREE.PlaneGeometry(100, 100);
        const floorMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: true,
            opacity: 0.1
        });
        this.floor = new THREE.Mesh(floorGeometry, floorMaterial);
        this.floor.rotation.x = -Math.PI / 2;
        this.floor.position.y = 0;
        this.floor.receiveShadow = true;
        this.scene.add(this.floor);
    }
    
    setupLighting() {
        // Ambient light for basic visibility
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // Main flickering lights
        this.lights = new LightingSystem(this.scene);
        this.lights.init();
    }
    
    setupControls() {
        this.controls = new GameControls(this.camera, this.renderer.domElement);
        this.controls.init();
        
        // Set up interaction detection
        this.controls.onMove = (position) => {
            this.checkInteractions(position);
        };
    }
    
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Interaction key
        document.addEventListener('keydown', (event) => {
            if (event.code === 'KeyE' && this.gameState.canInteract) {
                this.handleInteraction();
            }
        });
        
        // Mouse click for elevator buttons
        document.addEventListener('click', (event) => {
            if (this.currentScene === 'elevator') {
                this.handleElevatorClick(event);
            }
        });
    }
    
    setupAudio() {
        this.audioManager = new AudioManager();
        this.audioManager.init();
    }
    
    checkInteractions(playerPosition) {
        // Check if near elevator (placeholder logic)
        if (this.currentScene === 'lobby') {
            const elevatorPosition = new THREE.Vector3(0, 0, -5);
            const distance = playerPosition.distanceTo(elevatorPosition);
            
            if (distance < 3 && !this.gameState.nearElevator) {
                this.gameState.nearElevator = true;
                this.gameState.canInteract = true;
                this.showInteractionPrompt('Press E to enter elevator');
            } else if (distance >= 3 && this.gameState.nearElevator) {
                this.gameState.nearElevator = false;
                this.gameState.canInteract = false;
                this.hideInteractionPrompt();
            }
        }
    }
    
    handleInteraction() {
        if (this.currentScene === 'lobby' && this.gameState.nearElevator) {
            this.loadElevatorScene();
        }
    }
    
    handleElevatorClick(event) {
        // Raycast to detect elevator button clicks
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // This will be implemented when elevator scene is loaded
        if (this.elevatorScene && this.elevatorScene.buttons) {
            const intersects = this.raycaster.intersectObjects(this.elevatorScene.buttons);
            if (intersects.length > 0) {
                const buttonNumber = intersects[0].object.userData.buttonNumber;
                this.pressElevatorButton(buttonNumber);
            }
        }
    }
    
    pressElevatorButton(buttonNumber) {
        console.log(`Pressed elevator button: ${buttonNumber}`);
        this.gameState.elevatorSequence.push(buttonNumber);
        
        // Play button press sound
        this.audioManager.playSound('elevator_ding');
        
        // Check if sequence is complete
        if (this.gameState.elevatorSequence.length === this.gameState.correctSequence.length) {
            this.checkElevatorSequence();
        }
    }
    
    checkElevatorSequence() {
        const isCorrect = this.gameState.elevatorSequence.every(
            (num, index) => num === this.gameState.correctSequence[index]
        );
        
        if (isCorrect) {
            console.log('Correct sequence! Loading Other World...');
            setTimeout(() => {
                this.loadOtherWorldScene();
            }, 2000);
        } else {
            console.log('Wrong sequence! Triggering jumpscare...');
            this.triggerJumpscare();
        }
    }
    
    triggerJumpscare() {
        // Play jumpscare audio
        this.audioManager.playSound('jumpscare');
        
        // Flash screen red
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.backgroundColor = '#FF0000';
        flash.style.zIndex = '1000';
        flash.style.opacity = '0.8';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            document.body.removeChild(flash);
            this.resetToLobby();
        }, 500);
    }
    
    showInteractionPrompt(text) {
        const prompt = document.getElementById('interactionPrompt');
        prompt.textContent = text;
        prompt.style.opacity = '1';
    }
    
    hideInteractionPrompt() {
        const prompt = document.getElementById('interactionPrompt');
        prompt.style.opacity = '0';
    }
    
    // Scene loading methods
    loadLobbyScene() {
        this.currentScene = 'lobby';
        this.gameState.elevatorSequence = [];
        this.gameState.inElevator = false;
        
        // Clear current scene
        this.clearScene();
        
        // Load lobby
        this.lobbyScene = new LobbyScene(this.scene);
        this.lobbyScene.load().then(() => {
            console.log('Lobby scene loaded');
            this.camera.position.set(0, 1.7, 5);
            this.controls.enable();
        });
    }
    
    loadElevatorScene() {
        this.currentScene = 'elevator';
        this.gameState.inElevator = true;
        
        this.clearScene();
        
        this.elevatorScene = new ElevatorScene(this.scene);
        this.elevatorScene.load().then(() => {
            console.log('Elevator scene loaded');
            this.camera.position.set(0, 1.7, 0);
            this.controls.disable(); // Disable movement in elevator
        });
    }
    
    loadOtherWorldScene() {
        this.currentScene = 'otherworld';
        
        this.clearScene();
        
        this.otherworldScene = new OtherWorldScene(this.scene);
        this.otherworldScene.load().then(() => {
            console.log('Other World scene loaded');
            this.camera.position.set(0, 1.7, 0);
            this.controls.enable();
        });
    }
    
    resetToLobby() {
        this.loadLobbyScene();
    }
    
    clearScene() {
        // Remove all objects except lights and floor
        const objectsToRemove = [];
        this.scene.traverse((child) => {
            if (child !== this.floor && !child.isLight && child.parent === this.scene) {
                objectsToRemove.push(child);
            }
        });
        
        objectsToRemove.forEach(obj => {
            this.scene.remove(obj);
        });
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // Update controls
        if (this.controls) {
            this.controls.update(deltaTime);
        }
        
        // Update lighting effects
        if (this.lights) {
            this.lights.update(deltaTime);
        }
        
        // Update current scene
        if (this.currentScene === 'lobby' && this.lobbyScene) {
            this.lobbyScene.update(deltaTime);
        } else if (this.currentScene === 'elevator' && this.elevatorScene) {
            this.elevatorScene.update(deltaTime);
        } else if (this.currentScene === 'otherworld' && this.otherworldScene) {
            this.otherworldScene.update(deltaTime);
        }
        
        // Render
        this.renderer.render(this.scene, this.camera);
    }
    
    startGame() {
        // Hide menu, show game
        document.getElementById('mainMenu').style.display = 'none';
        document.getElementById('backgroundVideo').style.display = 'none';
        document.getElementById('gameCanvas').style.display = 'block';
        document.getElementById('gameUI').style.display = 'block';
        
        // Start with lobby scene
        this.loadLobbyScene();
        
        // Start game audio
        this.audioManager.startGameAudio();
    }
}

// Global game instance
let game = null;

// Initialize when page loads
window.addEventListener('load', () => {
    game = new DescentGame();
});
