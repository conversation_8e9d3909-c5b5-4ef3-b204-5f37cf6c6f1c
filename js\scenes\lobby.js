// DESCENT - Lobby Scene
class LobbyScene {
    constructor(scene) {
        this.scene = scene;
        this.loader = new THREE.GLTFLoader();
        this.textureLoader = new THREE.TextureLoader();
        
        this.objects = [];
        this.elevatorTrigger = null;
        this.loaded = false;
        
        // Animation mixers for any animated objects
        this.mixers = [];
    }
    
    async load() {
        console.log('Loading lobby scene...');
        
        try {
            // Load main lobby model
            await this.loadLobbyModel();
            
            // Create placeholder objects if model fails to load
            this.createPlaceholderObjects();
            
            // Set up elevator interaction trigger
            this.createElevatorTrigger();
            
            // Add atmospheric details
            this.addAtmosphericDetails();
            
            // Configure lighting for lobby
            if (window.game && window.game.lights) {
                window.game.lights.setLobbyLighting();
            }
            
            this.loaded = true;
            console.log('Lobby scene loaded successfully');
            
        } catch (error) {
            console.error('Error loading lobby scene:', error);
            this.createFallbackScene();
        }
    }
    
    async loadLobbyModel() {
        return new Promise((resolve, reject) => {
            this.loader.load(
                'assets/models/lobby.glb',
                (gltf) => {
                    console.log('Lobby model loaded');
                    
                    const model = gltf.scene;
                    model.position.set(0, 0, 0);
                    model.scale.set(1, 1, 1);
                    
                    // Enable shadows
                    model.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                            child.userData.collidable = true;
                            
                            // Enhance materials for horror atmosphere
                            if (child.material) {
                                child.material.roughness = 0.8;
                                child.material.metalness = 0.1;
                            }
                        }
                    });
                    
                    this.scene.add(model);
                    this.objects.push(model);
                    
                    // Set up animations if any
                    if (gltf.animations && gltf.animations.length > 0) {
                        const mixer = new THREE.AnimationMixer(model);
                        gltf.animations.forEach((clip) => {
                            mixer.clipAction(clip).play();
                        });
                        this.mixers.push(mixer);
                    }
                    
                    resolve();
                },
                (progress) => {
                    console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
                },
                (error) => {
                    console.warn('Failed to load lobby model, using placeholder');
                    resolve(); // Don't reject, use placeholder instead
                }
            );
        });
    }
    
    createPlaceholderObjects() {
        // Create basic lobby geometry as placeholder
        
        // Floor
        const floorGeometry = new THREE.PlaneGeometry(20, 20);
        const floorTexture = this.createCheckerboardTexture();
        const floorMaterial = new THREE.MeshLambertMaterial({ 
            map: floorTexture,
            color: 0x666666
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = 0;
        floor.receiveShadow = true;
        floor.userData.collidable = false; // Floor shouldn't block movement
        this.scene.add(floor);
        this.objects.push(floor);
        
        // Walls
        this.createWalls();
        
        // Furniture and props
        this.createFurniture();
        
        // Elevator door placeholder
        this.createElevatorDoor();
    }
    
    createWalls() {
        const wallHeight = 4;
        const wallThickness = 0.2;
        const roomSize = 10;
        
        const wallMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x444444,
            roughness: 0.9
        });
        
        // Back wall
        const backWall = new THREE.BoxGeometry(roomSize * 2, wallHeight, wallThickness);
        const backWallMesh = new THREE.Mesh(backWall, wallMaterial);
        backWallMesh.position.set(0, wallHeight / 2, -roomSize);
        backWallMesh.castShadow = true;
        backWallMesh.receiveShadow = true;
        backWallMesh.userData.collidable = true;
        this.scene.add(backWallMesh);
        this.objects.push(backWallMesh);
        
        // Side walls
        const sideWall = new THREE.BoxGeometry(wallThickness, wallHeight, roomSize * 2);
        
        const leftWall = new THREE.Mesh(sideWall, wallMaterial);
        leftWall.position.set(-roomSize, wallHeight / 2, 0);
        leftWall.castShadow = true;
        leftWall.receiveShadow = true;
        leftWall.userData.collidable = true;
        this.scene.add(leftWall);
        this.objects.push(leftWall);
        
        const rightWall = new THREE.Mesh(sideWall, wallMaterial);
        rightWall.position.set(roomSize, wallHeight / 2, 0);
        rightWall.castShadow = true;
        rightWall.receiveShadow = true;
        rightWall.userData.collidable = true;
        this.scene.add(rightWall);
        this.objects.push(rightWall);
    }
    
    createFurniture() {
        // Old reception desk
        const deskGeometry = new THREE.BoxGeometry(3, 1, 1.5);
        const deskMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const desk = new THREE.Mesh(deskGeometry, deskMaterial);
        desk.position.set(-3, 0.5, -7);
        desk.castShadow = true;
        desk.receiveShadow = true;
        desk.userData.collidable = true;
        this.scene.add(desk);
        this.objects.push(desk);
        
        // Old chairs
        this.createChair(-5, 0, -3);
        this.createChair(3, 0, -2);
        this.createChair(6, 0, 4);
        
        // Trash and debris
        this.createDebris();
    }
    
    createChair(x, y, z) {
        const chairGroup = new THREE.Group();
        
        // Seat
        const seatGeometry = new THREE.BoxGeometry(0.8, 0.1, 0.8);
        const chairMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
        const seat = new THREE.Mesh(seatGeometry, chairMaterial);
        seat.position.y = 0.5;
        chairGroup.add(seat);
        
        // Backrest
        const backGeometry = new THREE.BoxGeometry(0.8, 1, 0.1);
        const back = new THREE.Mesh(backGeometry, chairMaterial);
        back.position.set(0, 1, -0.35);
        chairGroup.add(back);
        
        // Legs
        const legGeometry = new THREE.BoxGeometry(0.05, 0.5, 0.05);
        const legPositions = [
            [-0.35, 0.25, -0.35],
            [0.35, 0.25, -0.35],
            [-0.35, 0.25, 0.35],
            [0.35, 0.25, 0.35]
        ];
        
        legPositions.forEach(pos => {
            const leg = new THREE.Mesh(legGeometry, chairMaterial);
            leg.position.set(pos[0], pos[1], pos[2]);
            chairGroup.add(leg);
        });
        
        chairGroup.position.set(x, y, z);
        chairGroup.rotation.y = Math.random() * Math.PI * 2;
        
        chairGroup.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                child.userData.collidable = true;
            }
        });
        
        this.scene.add(chairGroup);
        this.objects.push(chairGroup);
    }
    
    createDebris() {
        // Scattered papers, bottles, etc.
        for (let i = 0; i < 10; i++) {
            const debrisGeometry = new THREE.BoxGeometry(
                0.1 + Math.random() * 0.3,
                0.05,
                0.1 + Math.random() * 0.3
            );
            const debrisMaterial = new THREE.MeshLambertMaterial({ 
                color: new THREE.Color().setHSL(0, 0, 0.2 + Math.random() * 0.3)
            });
            const debris = new THREE.Mesh(debrisGeometry, debrisMaterial);
            
            debris.position.set(
                (Math.random() - 0.5) * 15,
                0.025,
                (Math.random() - 0.5) * 15
            );
            debris.rotation.y = Math.random() * Math.PI * 2;
            
            debris.castShadow = true;
            debris.receiveShadow = true;
            debris.userData.collidable = false; // Small debris shouldn't block movement
            
            this.scene.add(debris);
            this.objects.push(debris);
        }
    }
    
    createElevatorDoor() {
        // Elevator door frame
        const frameGeometry = new THREE.BoxGeometry(2.5, 3, 0.2);
        const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const frame = new THREE.Mesh(frameGeometry, frameMaterial);
        frame.position.set(0, 1.5, -9.9);
        frame.castShadow = true;
        frame.receiveShadow = true;
        frame.userData.collidable = true;
        this.scene.add(frame);
        this.objects.push(frame);
        
        // Elevator doors (closed)
        const doorGeometry = new THREE.BoxGeometry(1.2, 2.8, 0.1);
        const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        
        const leftDoor = new THREE.Mesh(doorGeometry, doorMaterial);
        leftDoor.position.set(-0.6, 1.4, -9.8);
        leftDoor.castShadow = true;
        leftDoor.receiveShadow = true;
        leftDoor.userData.collidable = true;
        this.scene.add(leftDoor);
        this.objects.push(leftDoor);
        
        const rightDoor = new THREE.Mesh(doorGeometry, doorMaterial);
        rightDoor.position.set(0.6, 1.4, -9.8);
        rightDoor.castShadow = true;
        rightDoor.receiveShadow = true;
        rightDoor.userData.collidable = true;
        this.scene.add(rightDoor);
        this.objects.push(rightDoor);
    }
    
    createElevatorTrigger() {
        // Invisible trigger zone in front of elevator
        const triggerGeometry = new THREE.BoxGeometry(3, 3, 2);
        const triggerMaterial = new THREE.MeshBasicMaterial({ 
            transparent: true, 
            opacity: 0,
            visible: false
        });
        this.elevatorTrigger = new THREE.Mesh(triggerGeometry, triggerMaterial);
        this.elevatorTrigger.position.set(0, 1.5, -7);
        this.elevatorTrigger.userData.isElevatorTrigger = true;
        this.scene.add(this.elevatorTrigger);
    }
    
    addAtmosphericDetails() {
        // Dust particles (simple particle system)
        this.createDustParticles();
        
        // Flickering light fixtures
        this.createLightFixtures();
    }
    
    createDustParticles() {
        const particleCount = 50;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 20;     // x
            positions[i + 1] = Math.random() * 4;          // y
            positions[i + 2] = (Math.random() - 0.5) * 20; // z
        }
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.02,
            transparent: true,
            opacity: 0.3
        });
        
        const dustSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(dustSystem);
        this.objects.push(dustSystem);
    }
    
    createLightFixtures() {
        // Simple light fixture models
        const fixtureGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2, 8);
        const fixtureMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        
        const positions = [
            { x: 0, z: 0 },
            { x: -5, z: -5 },
            { x: 5, z: -5 }
        ];
        
        positions.forEach(pos => {
            const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
            fixture.position.set(pos.x, 3.8, pos.z);
            fixture.castShadow = true;
            fixture.receiveShadow = true;
            this.scene.add(fixture);
            this.objects.push(fixture);
        });
    }
    
    createCheckerboardTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const context = canvas.getContext('2d');
        
        context.fillStyle = '#444444';
        context.fillRect(0, 0, 64, 64);
        
        context.fillStyle = '#555555';
        for (let i = 0; i < 8; i++) {
            for (let j = 0; j < 8; j++) {
                if ((i + j) % 2 === 0) {
                    context.fillRect(i * 8, j * 8, 8, 8);
                }
            }
        }
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        
        return texture;
    }
    
    createFallbackScene() {
        console.log('Creating fallback lobby scene');
        this.createPlaceholderObjects();
        this.createElevatorTrigger();
        this.loaded = true;
    }
    
    update(deltaTime) {
        // Update any animations
        this.mixers.forEach(mixer => {
            mixer.update(deltaTime);
        });
        
        // Animate dust particles
        this.objects.forEach(obj => {
            if (obj.isPoints) {
                obj.rotation.y += deltaTime * 0.1;
                
                // Slowly move particles
                const positions = obj.geometry.attributes.position.array;
                for (let i = 1; i < positions.length; i += 3) {
                    positions[i] += deltaTime * 0.1; // Move up slowly
                    if (positions[i] > 4) {
                        positions[i] = 0; // Reset to bottom
                    }
                }
                obj.geometry.attributes.position.needsUpdate = true;
            }
        });
    }
    
    dispose() {
        this.objects.forEach(obj => {
            this.scene.remove(obj);
        });
        this.objects = [];
        this.mixers = [];
        this.loaded = false;
    }
}
